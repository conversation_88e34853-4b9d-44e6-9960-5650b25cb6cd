'use client'

import { 
  ChevronLeftIcon, 
  ChevronRightIcon, 
  ChevronDoubleLeftIcon, 
  ChevronDoubleRightIcon 
} from '@heroicons/react/24/outline'
import { TablePaginationProps } from './types/table.types'
import { cn } from './utils/tableHelpers'

export const TablePagination = ({ table, className }: TablePaginationProps) => {
  const currentPage = table.getState().pagination.pageIndex + 1
  const totalPages = table.getPageCount()
  const pageSize = table.getState().pagination.pageSize
  const totalRows = table.getFilteredRowModel().rows.length

  const startRow = (currentPage - 1) * pageSize + 1
  const endRow = Math.min(currentPage * pageSize, totalRows)

  const pageSizeOptions = [5, 10, 20, 50, 100]

  return (
    <div className={cn('flex items-center justify-between p-4 border-t border-slate-200', className)}>
      {/* Page size selector */}
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-700">Show</span>
        <select
          value={pageSize}
          onChange={(e) => table.setPageSize(Number(e.target.value))}
          className="h-8 text-sm border border-gray-300 rounded-md focus:border-[#00B2A1] focus:ring-[#00B2A1]/20"
        >
          {pageSizeOptions.map((size) => (
            <option key={size} value={size}>
              {size}
            </option>
          ))}
        </select>
        <span className="text-sm text-gray-700">entries</span>
      </div>

      {/* Pagination info */}
      <div className="text-sm text-gray-700">
        Showing {startRow} to {endRow} of {totalRows} entries
      </div>

      {/* Pagination controls */}
      <div className="flex items-center gap-2">
        <button
          onClick={() => table.setPageIndex(0)}
          disabled={!table.getCanPreviousPage()}
          className={cn(
            'p-2 rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed',
            'focus:border-[#00B2A1] focus:ring-2 focus:ring-[#00B2A1]/20'
          )}
        >
          <ChevronDoubleLeftIcon className="w-4 h-4" />
        </button>
        
        <button
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
          className={cn(
            'p-2 rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed',
            'focus:border-[#00B2A1] focus:ring-2 focus:ring-[#00B2A1]/20'
          )}
        >
          <ChevronLeftIcon className="w-4 h-4" />
        </button>

        <div className="flex items-center gap-1">
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const page = i + 1
            const isActive = page === currentPage
            
            return (
              <button
                key={page}
                onClick={() => table.setPageIndex(page - 1)}
                className={cn(
                  'px-3 py-1 text-sm rounded-md border',
                  isActive 
                    ? 'bg-[#00B2A1] text-white border-[#00B2A1]' 
                    : 'border-gray-300 hover:bg-gray-50 focus:border-[#00B2A1] focus:ring-2 focus:ring-[#00B2A1]/20'
                )}
              >
                {page}
              </button>
            )
          })}
        </div>

        <button
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
          className={cn(
            'p-2 rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed',
            'focus:border-[#00B2A1] focus:ring-2 focus:ring-[#00B2A1]/20'
          )}
        >
          <ChevronRightIcon className="w-4 h-4" />
        </button>
        
        <button
          onClick={() => table.setPageIndex(table.getPageCount() - 1)}
          disabled={!table.getCanNextPage()}
          className={cn(
            'p-2 rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed',
            'focus:border-[#00B2A1] focus:ring-2 focus:ring-[#00B2A1]/20'
          )}
        >
          <ChevronDoubleRightIcon className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
}