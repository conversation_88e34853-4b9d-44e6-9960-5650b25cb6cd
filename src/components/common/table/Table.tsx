'use client'

import React from 'react'
import { flexRender } from '@tanstack/react-table'
import { ChevronRightIcon, ChevronDownIcon } from '@heroicons/react/24/outline'
import { TableProps } from './types/table.types'
import { useTableState } from './hooks/useTableState'
import { TableHeader } from './TableHeader'
import { TableRow } from './TableRow'
import { TableCell } from './TableCell'
import { TablePagination } from './TablePagination'
import { TableColumns } from './TableColumns'
import { cn, getRowLevel, formatCellValue } from './utils/tableHelpers'

export const Table = <TData,>({
  data,
  columns,
  features,
  initialState,
  onStateChange,
  onTableReady,
  className,
  enableMultiSort = true,
  enableSortingRemoval = true,
  maxMultiSortColCount = 3,
  showColumnManagement = false,
  density = 'normal',
}: TableProps<TData>) => {
  const { table, features: enabledFeatures } = useTableState({
    data,
    columns,
    features,
    initialState,
    onStateChange,
    enableMultiSort,
    enableSortingRemoval,
    maxMultiSortColCount,
  })

  // Notify parent component that table is ready
  React.useEffect(() => {
    onTableReady?.(table)
  }, [table, onTableReady])

  const rows = table.getRowModel().rows

  return (
    <div className={cn('bg-white rounded-xl border border-slate-200 shadow-sm', className)}>
      {/* Column Management Only */}
      {showColumnManagement && (
        <div className="border-b border-slate-200">
          <div className="p-4 bg-gray-50/50">
            <div className="flex justify-end">
              <TableColumns table={table} />
            </div>
          </div>
        </div>
      )}

      {/* Table */}
      <div className="max-h-[calc(100vh-230px)] overflow-auto">
        <table className="w-full">
          <TableHeader table={table} density={density} />
          <tbody>
            {rows.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="p-8 text-center text-gray-500">
                  <div className="flex flex-col items-center gap-2">
                    <div className="w-12 h-12 text-gray-400">
                      <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div className="text-lg font-medium text-gray-700">No data available</div>
                    <div className="text-sm text-gray-500">Try adjusting your filters or search criteria</div>
                  </div>
                </td>
              </tr>
            ) : (
              rows.map((row, rowIndex) => {
                const level = getRowLevel(row)
                const isExpanded = row.getIsExpanded()
                const canExpand = row.getCanExpand()
                const isSelected = row.getIsSelected()
                const isGrouped = row.getIsGrouped()

                return (
                  <TableRow 
                    key={row.id}
                    isSelected={isSelected}
                    level={level}
                    index={rowIndex}
                  >
                    {row.getVisibleCells().map((cell, cellIndex) => {
                      const isFirstCell = cellIndex === 0
                      const indentLevel = level * 16

                      return (
                        <TableCell 
                          key={cell.id}
                          density={density}
                          className={cn(
                            isFirstCell && level > 0 && `pl-${8 + level * 4}`,
                            isGrouped && 'font-semibold'
                          )}
                        >
                          <div className="flex items-center justify-center gap-2">
                            {/* Expand/collapse button for first cell */}
                            {isFirstCell && canExpand && (
                              <button
                                onClick={row.getToggleExpandedHandler()}
                                className="flex-shrink-0 p-1 hover:bg-gray-100 rounded"
                                style={{ marginLeft: indentLevel }}
                              >
                                {isExpanded ? (
                                  <ChevronDownIcon className="w-4 h-4 text-gray-500" />
                                ) : (
                                  <ChevronRightIcon className="w-4 h-4 text-gray-500" />
                                )}
                              </button>
                            )}

                            {/* Cell content */}
                            <div className="flex-1">
                              {isGrouped ? (
                                <div className="flex items-center gap-2">
                                  <span>{formatCellValue(cell.getValue())}</span>
                                  <span className="text-xs bg-gray-100 text-gray-600 px-1.5 py-0.5 rounded-full">
                                    {row.subRows?.length || 0}
                                  </span>
                                </div>
                              ) : (
                                flexRender(cell.column.columnDef.cell, cell.getContext())
                              )}
                            </div>
                          </div>
                        </TableCell>
                      )
                    })}
                  </TableRow>
                )
              })
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {enabledFeatures.pagination && (
        <TablePagination table={table} />
      )}
    </div>
  )
}