import { ColumnDef, Table as TanStackTable } from '@tanstack/react-table'
import { SelectOption } from '../../select'

export type TableDensity = 'normal' | 'condensed'

export interface TableFeatures {
  sorting?: boolean
  filtering?: boolean
  globalFilter?: boolean
  pagination?: boolean
  grouping?: boolean
  expanding?: boolean
  columnResizing?: boolean
  columnOrdering?: boolean
  columnPinning?: boolean
  rowPinning?: boolean
  rowSelection?: boolean
}

export interface PaginationState {
  pageIndex: number
  pageSize: number
}

export interface BulkAction<TData = any> {
  id: string
  label: string
  icon?: React.ReactNode
  variant?: 'primary' | 'secondary' | 'danger'
  onClick: (selectedRows: TData[]) => void
  disabled?: (selectedRows: TData[]) => boolean
}

export interface TableProps<TData> {
  data: TData[]
  columns: ColumnDef<TData>[]
  features?: TableFeatures
  initialState?: {
    pagination?: PaginationState
    sorting?: any[]
    columnFilters?: any[]
    globalFilter?: string
    columnVisibility?: Record<string, boolean>
    columnOrder?: string[]
    columnPinning?: {
      left?: string[]
      right?: string[]
    }
    rowPinning?: {
      top?: string[]
      bottom?: string[]
    }
    rowSelection?: Record<string, boolean>
  }
  onStateChange?: (state: any) => void
  onTableReady?: (table: TanStackTable<TData>) => void
  className?: string
  enableMultiSort?: boolean
  enableSortingRemoval?: boolean
  maxMultiSortColCount?: number
  showColumnManagement?: boolean
  density?: TableDensity
}

export interface TableCellProps {
  children: React.ReactNode
  className?: string
  onClick?: () => void
  density?: TableDensity
}

export interface TableHeaderProps {
  table: TanStackTable<any>
  className?: string
  density?: TableDensity
}

export interface TableRowProps {
  children: React.ReactNode
  className?: string
  isSelected?: boolean
  isPinned?: boolean
  level?: number
  index?: number
}

export interface TablePaginationProps {
  table: TanStackTable<any>
  className?: string
}

export interface TableFiltersProps {
  table: TanStackTable<any>
  className?: string
}

export interface ColumnManagementProps {
  table: TanStackTable<any>
  className?: string
}

// Editable cell types
export interface EditableCellConfig<TData = any, TValue = any> {
  type: 'select' | 'input' | 'textarea' | 'checkbox' | 'custom'
  validate?: (value: TValue) => string | null
  disabled?: boolean | ((row: TData) => boolean)
  autoSave?: boolean
  onSave?: (rowData: TData, newValue: TValue) => void | Promise<void>
  onCancel?: (rowData: TData) => void
}

export interface EditableSelectConfig<TData = any> extends EditableCellConfig<TData, string> {
  type: 'select'
  options: SelectOption[] | ((row: TData) => SelectOption[])
  placeholder?: string
  clearable?: boolean
  searchable?: boolean
  renderDisplay?: (value: string, option?: SelectOption, row?: TData) => React.ReactNode
}

export interface EditableInputConfig<TData = any> extends EditableCellConfig<TData, string> {
  type: 'input'
  inputType?: 'text' | 'number' | 'email' | 'tel' | 'url'
  placeholder?: string
  maxLength?: number
  minLength?: number
}

export interface EditableTextareaConfig<TData = any> extends EditableCellConfig<TData, string> {
  type: 'textarea'
  placeholder?: string
  rows?: number
  maxLength?: number
}

export interface EditableCheckboxConfig<TData = any> extends EditableCellConfig<TData, boolean> {
  type: 'checkbox'
  label?: string
}

export interface EditableCustomConfig<TData = any, TValue = any> extends EditableCellConfig<TData, TValue> {
  type: 'custom'
  renderEdit: (
    value: TValue,
    onChange: (newValue: TValue) => void,
    onCommit: () => void,
    onCancel: () => void,
    row: TData
  ) => React.ReactNode
  renderDisplay?: (value: TValue, row: TData) => React.ReactNode
}

export type EditableConfig<TData = any> = 
  | EditableSelectConfig<TData>
  | EditableInputConfig<TData>
  | EditableTextareaConfig<TData>
  | EditableCheckboxConfig<TData>
  | EditableCustomConfig<TData>

// Extended column definition with editable support
export interface EditableColumnDef<TData, TValue = unknown> extends ColumnDef<TData, TValue> {
  editable?: EditableConfig<TData>
}

// Table state for tracking editing
export interface EditingState {
  [key: string]: { // rowId
    [key: string]: boolean // columnId: isEditing
  }
}

// Extended table features to include editing
export interface ExtendedTableFeatures extends TableFeatures {
  editing?: boolean
}

// Extended table props with editing support
export interface EditableTableProps<TData> extends Omit<TableProps<TData>, 'columns' | 'features'> {
  columns: EditableColumnDef<TData>[]
  features?: ExtendedTableFeatures
  onCellSave?: (rowId: string, columnId: string, oldValue: any, newValue: any) => void | Promise<void>
  onCellEditStart?: (rowId: string, columnId: string) => void
  onCellEditCancel?: (rowId: string, columnId: string) => void
}